import { sensePriceConfigInterface } from '../../interfaces';

export const generateSensePriceConfig = (
  question: string,
  currency: string,
  priceType: string,
  recurringBasis: string,
  thankYouMessage: string,
) => {
  let payload: sensePriceConfigInterface = {
    question: question,
    currency: currency,
    priceType: priceType,
    recurringBasis: priceType === 'recurring' ? recurringBasis : undefined,
    thankYouMessage: thankYouMessage || 'Thank you for your response!',
  };

  return payload;
};
